name: Playwright Tests
on:
  push:
    branches: [main, dev]
  workflow_dispatch:

concurrency:
  group: playwright-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test:
    name: E2E Tests
    timeout-minutes: 30
    runs-on: ubuntu-latest
    container:
      image: mcr.microsoft.com/playwright:v1.53.0-jammy
      options: --ipc=host
    env:
      CI: true
      PLAYWRIGHT_BASE_URL: 'http://localhost:8081'
      GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
      SUPABASE_SERVICE_KEY: ${{ secrets.SUPABASE_SERVICE_KEY }}

    steps:
      - name: Setup workspace
        uses: ./.github/actions/setup-workspace
        with:
          cache-key-suffix: -playwright

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          cache-key-suffix: -playwright
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}

      - name: <PERSON><PERSON> Playwright browsers
        uses: actions/cache@v4
        id: playwright-cache
        with:
          path: /home/<USER>/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ hashFiles('package-lock.json') }}

      - name: Install Playwright browsers
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        run: npx playwright install --with-deps

      - name: Run Playwright tests
        run: npx playwright test --reporter=html,github

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 7
          if-no-files-found: ignore
